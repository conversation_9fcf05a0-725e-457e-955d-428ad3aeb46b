import { SuiService } from '../sui/sui.service';
import { AuthService } from '../auth/auth.service';
import { WalrusService } from '../storage/walrus/walrus.service';
import { SealService } from '../storage/seal/seal.service';
import { AccessControlService } from '../access-control/access-control.service';
export interface FileUploadRequest {
    filename: string;
    fileSize: number;
    contentType: string;
    fileData: Buffer;
    walrusCid?: string;
    enableEncryption?: boolean;
}
export interface EncryptedFileUploadRequest extends FileUploadRequest {
    enableEncryption: true;
}
export interface EncryptedFileMetadata {
    filename: string;
    fileSize: number;
    uploadTimestamp: number;
    uploader: string;
    isEncrypted: boolean;
    encryptionKeys?: {
        publicKey: string;
        secretKey: string;
    };
}
export interface FileAccessRequest {
    fileCid: string;
    requesterAddress?: string;
}
export interface FileUploadResponse {
    success: boolean;
    fileCid: string;
    transactionDigest: string;
    walrusCid: string;
    message: string;
}
export interface FileAccessResponse {
    success: boolean;
    authorized: boolean;
    fileMetadata?: {
        filename: string;
        fileSize: number;
        uploadTimestamp: number;
        uploader: string;
    };
    walrusCid?: string;
    message: string;
}
export declare class FileService {
    private readonly suiService;
    private readonly authService;
    private readonly walrusService;
    private readonly sealService;
    private readonly accessControlService;
    private readonly logger;
    private uploadedFiles;
    private testModeFiles;
    constructor(suiService: SuiService, authService: AuthService, walrusService: WalrusService, sealService: SealService, accessControlService: AccessControlService);
    uploadFile(token: string, uploadRequest: FileUploadRequest): Promise<FileUploadResponse>;
    accessFile(token: string, accessRequest: FileAccessRequest): Promise<FileAccessResponse>;
    grantFileAccess(token: string, fileCid: string, recipientAddress: string): Promise<{
        success: boolean;
        message: string;
        transactionDigest?: string;
    }>;
    revokeFileAccess(token: string, fileCid: string, addressToRemove: string): Promise<{
        success: boolean;
        message: string;
        transactionDigest?: string;
    }>;
    downloadFile(token: string, fileCid: string): Promise<{
        success: boolean;
        fileData?: Buffer;
        filename?: string;
        contentType?: string;
        message: string;
        isEncrypted?: boolean;
    }>;
    downloadAndDecryptFile(token: string, fileCid: string, sessionKey: any, txBytes: Uint8Array): Promise<{
        success: boolean;
        fileData?: Buffer;
        filename?: string;
        contentType?: string;
        message: string;
    }>;
    downloadEncryptedFile(token: string, fileCid: string, symmetricKey: string): Promise<{
        success: boolean;
        fileData?: Buffer;
        filename?: string;
        contentType?: string;
        message: string;
    }>;
    uploadFileNoAuth(uploadRequest: FileUploadRequest): Promise<FileUploadResponse>;
    downloadFileNoAuth(fileCid: string): Promise<{
        success: boolean;
        fileData?: Buffer;
        filename?: string;
        contentType?: string;
        message: string;
    }>;
    listUserFiles(token: string): Promise<{
        success: boolean;
        files: Array<{
            cid: string;
            filename: string;
            fileSize: number;
            uploadTimestamp: number;
            uploader: string;
            isOwner: boolean;
        }>;
        message: string;
    }>;
    listUserFilesNoAuth(): Promise<{
        success: boolean;
        files: Array<{
            cid: string;
            filename: string;
            fileSize: number;
            uploadTimestamp: number;
            uploader: string;
            isOwner: boolean;
        }>;
        message: string;
    }>;
    clearUserFiles(token: string): Promise<{
        success: boolean;
        message: string;
    }>;
    clearUserFilesNoAuth(): Promise<{
        success: boolean;
        message: string;
    }>;
}
