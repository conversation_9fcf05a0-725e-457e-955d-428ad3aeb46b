"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var FileController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const file_service_1 = require("./file.service");
const auth_guard_1 = require("../auth/auth.guard");
let FileController = FileController_1 = class FileController {
    fileService;
    logger = new common_1.Logger(FileController_1.name);
    constructor(fileService) {
        this.fileService = fileService;
    }
    async uploadFile(authorization, file, user) {
        try {
            if (!file) {
                throw new common_1.HttpException('No file provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const uploadRequest = {
                filename: file.originalname,
                fileSize: file.size,
                contentType: file.mimetype,
                fileData: file.buffer,
            };
            const token = authorization.substring(7);
            const result = await this.fileService.uploadFile(token, uploadRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    fileCid: result.fileCid,
                    transactionDigest: result.transactionDigest,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to upload file', error);
            throw new common_1.HttpException(error.message || 'Failed to upload file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadFileMetadata(authorization, uploadRequest, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.uploadFile(token, uploadRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    fileCid: result.fileCid,
                    transactionDigest: result.transactionDigest,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to upload file metadata', error);
            throw new common_1.HttpException(error.message || 'Failed to upload file metadata', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getFileInfo(cid, authorization, user) {
        try {
            const token = authorization.substring(7);
            const accessRequest = { fileCid: cid };
            const result = await this.fileService.accessFile(token, accessRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            if (!result.authorized) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.FORBIDDEN);
            }
            return {
                success: true,
                data: {
                    fileMetadata: result.fileMetadata,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to access file', error);
            throw new common_1.HttpException('Failed to access file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async downloadFile(cid, authorization, user, res) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.downloadFile(token, cid);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            res.setHeader('Content-Type', result.contentType || 'application/octet-stream');
            res.setHeader('Content-Length', result.fileData.length);
            if (result.filename) {
                res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
            }
            res.send(result.fileData);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to download file', error);
            throw new common_1.HttpException('Failed to download file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async grantFileAccess(cid, body, authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.grantFileAccess(token, cid, body.recipientAddress);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    transactionDigest: result.transactionDigest,
                },
                message: result.message,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to grant file access', error);
            throw new common_1.HttpException('Failed to grant file access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async revokeFileAccess(cid, body, authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.revokeFileAccess(token, cid, body.addressToRemove);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    transactionDigest: result.transactionDigest,
                },
                message: result.message,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to revoke file access', error);
            throw new common_1.HttpException('Failed to revoke file access', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async listUserFiles(authorization, user) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.listUserFiles(token);
            return {
                success: result.success,
                data: {
                    files: result.files,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files', error);
            throw new common_1.HttpException('Failed to list user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadFileTest(file) {
        try {
            if (!file) {
                throw new common_1.HttpException('No file provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const uploadRequest = {
                filename: file.originalname,
                fileSize: file.size,
                contentType: file.mimetype,
                fileData: file.buffer,
            };
            const result = await this.fileService.uploadFileNoAuth(uploadRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    fileCid: result.fileCid,
                    transactionDigest: result.transactionDigest,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to upload file (test)', error);
            throw new common_1.HttpException(error.message || 'Failed to upload file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async downloadFileTest(cid, res) {
        try {
            const result = await this.fileService.downloadFileNoAuth(cid);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            res.setHeader('Content-Type', result.contentType || 'application/octet-stream');
            res.setHeader('Content-Length', result.fileData.length);
            if (result.filename) {
                res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
            }
            res.send(result.fileData);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to download file (test)', error);
            throw new common_1.HttpException('Failed to download file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getWalrusStatus() {
        try {
            const walrusService = this.fileService['walrusService'];
            const status = walrusService.getConfigurationStatus();
            const validation = walrusService.validateConfiguration();
            return {
                success: true,
                data: {
                    ...status,
                    validation,
                    environment: process.env.NODE_ENV || 'development',
                },
                message: 'Walrus configuration status retrieved',
            };
        }
        catch (error) {
            this.logger.error('Failed to get Walrus status', error);
            throw new common_1.HttpException('Failed to get Walrus status', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getWalletInfo() {
        try {
            const walrusService = this.fileService['walrusService'];
            const walletInfo = await walrusService.getWalletInfo();
            return {
                success: true,
                data: walletInfo,
                message: 'Wallet information retrieved',
            };
        }
        catch (error) {
            this.logger.error('Failed to get wallet info', error);
            throw new common_1.HttpException('Failed to get wallet info', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadEncryptedFile(authorization, file, user) {
        try {
            if (!file) {
                throw new common_1.HttpException('No file provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const uploadRequest = {
                filename: file.originalname,
                fileSize: file.size,
                contentType: file.mimetype,
                fileData: file.buffer,
                enableEncryption: true,
            };
            const token = authorization.substring(7);
            const result = await this.fileService.uploadFile(token, uploadRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    fileCid: result.fileCid,
                    transactionDigest: result.transactionDigest,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to upload encrypted file', error);
            throw new common_1.HttpException(error.message || 'Failed to upload encrypted file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async downloadEncryptedFile(cid, body, authorization, user, res) {
        try {
            const token = authorization.substring(7);
            const result = await this.fileService.downloadEncryptedFile(token, cid, body.secretKey);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            res.setHeader('Content-Type', result.contentType || 'application/octet-stream');
            res.setHeader('Content-Length', result.fileData.length);
            if (result.filename) {
                res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
            }
            res.send(result.fileData);
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            this.logger.error('Failed to download encrypted file', error);
            throw new common_1.HttpException('Failed to download encrypted file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async uploadEncryptedFileTest(file) {
        try {
            if (!file) {
                throw new common_1.HttpException('No file provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const uploadRequest = {
                filename: file.originalname,
                fileSize: file.size,
                contentType: file.mimetype,
                fileData: file.buffer,
                enableEncryption: true,
            };
            const result = await this.fileService.uploadFileNoAuth(uploadRequest);
            if (!result.success) {
                throw new common_1.HttpException(result.message, common_1.HttpStatus.BAD_REQUEST);
            }
            return {
                success: true,
                data: {
                    fileCid: result.fileCid,
                    transactionDigest: result.transactionDigest,
                    walrusCid: result.walrusCid,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to upload encrypted file (test)', error);
            throw new common_1.HttpException(error.message || 'Failed to upload encrypted file', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSealStatus() {
        try {
            const sealService = this.fileService['sealService'];
            return {
                success: true,
                data: {
                    isReady: sealService.isReady(),
                    version: sealService.getVersion(),
                },
                message: 'SEAL encryption service status retrieved',
            };
        }
        catch (error) {
            this.logger.error('Failed to get SEAL status', error);
            throw new common_1.HttpException('Failed to get SEAL status', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async listUserFilesTest() {
        try {
            const result = await this.fileService.listUserFilesNoAuth();
            return {
                success: result.success,
                files: result.files,
                data: {
                    files: result.files,
                },
                message: result.message,
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files (test)', error);
            throw new common_1.HttpException('Failed to list user files', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.FileController = FileController;
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Post)('upload-metadata'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "uploadFileMetadata", null);
__decorate([
    (0, common_1.Get)(':cid/info'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Headers)('authorization')),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "getFileInfo", null);
__decorate([
    (0, common_1.Get)(':cid/download'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Headers)('authorization')),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "downloadFile", null);
__decorate([
    (0, common_1.Post)(':cid/grant-access'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __param(3, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "grantFileAccess", null);
__decorate([
    (0, common_1.Post)(':cid/revoke-access'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __param(3, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "revokeFileAccess", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "listUserFiles", null);
__decorate([
    (0, common_1.Post)('upload-test'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "uploadFileTest", null);
__decorate([
    (0, common_1.Get)(':cid/download-test'),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "downloadFileTest", null);
__decorate([
    (0, common_1.Get)('walrus-status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FileController.prototype, "getWalrusStatus", null);
__decorate([
    (0, common_1.Get)('wallet-info'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FileController.prototype, "getWalletInfo", null);
__decorate([
    (0, common_1.Post)('upload-encrypted'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.Headers)('authorization')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, auth_guard_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "uploadEncryptedFile", null);
__decorate([
    (0, common_1.Post)(':cid/download-encrypted'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)('cid')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __param(3, (0, auth_guard_1.CurrentUser)()),
    __param(4, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, Object, Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "downloadEncryptedFile", null);
__decorate([
    (0, common_1.Post)('upload-encrypted-test'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FileController.prototype, "uploadEncryptedFileTest", null);
__decorate([
    (0, common_1.Get)('seal-status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FileController.prototype, "getSealStatus", null);
__decorate([
    (0, common_1.Get)('list-test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FileController.prototype, "listUserFilesTest", null);
exports.FileController = FileController = FileController_1 = __decorate([
    (0, common_1.Controller)('file'),
    __metadata("design:paramtypes", [file_service_1.FileService])
], FileController);
//# sourceMappingURL=file.controller.js.map